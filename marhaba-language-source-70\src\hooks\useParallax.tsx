import { useEffect, useState } from 'react';

export const useParallax = (speed: number = 0.5) => {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const parallaxStyle = {
    transform: `translateY(${scrollY * speed}px)`,
  };

  return { scrollY, parallaxStyle };
};

export const useParallaxMultiple = (elements: { speed: number }[]) => {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const getParallaxStyle = (index: number) => ({
    transform: `translateY(${scrollY * elements[index].speed}px)`,
  });

  return { scrollY, getParallaxStyle };
};
