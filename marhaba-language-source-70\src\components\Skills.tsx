
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useTranslation } from "@/hooks/useTranslation";
import { useRTL } from "@/hooks/useRTL";
import {
  Code2,
  Database,
  Globe,
  Smartphone,
  Server,
  GitBranch,
  Palette,
  Zap,
  Shield,
  Cloud
} from "lucide-react";

export const Skills = () => {
  const { t } = useTranslation();
  const { isRTL, flexRow } = useRTL();

  const skillCategories = [
    {
      title: t("frontendDevelopment"),
      icon: <Code2 className="w-6 h-6" />,
      color: "from-blue-500 to-cyan-500",
      skills: [
        { name: "React.js", level: 95, color: "bg-blue-500" },
        { name: "Next.js", level: 90, color: "bg-gray-800" },
        { name: "TypeScript", level: 88, color: "bg-blue-600" },
        { name: "Tailwind CSS", level: 92, color: "bg-teal-500" },
        { name: "HTML/CSS", level: 95, color: "bg-orange-500" },
        { name: "Vue.js", level: 82, color: "bg-green-500" },
      ],
    },
    {
      title: t("backendDevelopment"),
      icon: <Server className="w-6 h-6" />,
      color: "from-green-500 to-emerald-500",
      skills: [
        { name: "Node.js", level: 85, color: "bg-green-600" },
        { name: "Express.js", level: 82, color: "bg-gray-700" },
        { name: "PostgreSQL", level: 80, color: "bg-blue-700" },
        { name: "MongoDB", level: 78, color: "bg-green-700" },
        { name: "REST APIs", level: 90, color: "bg-purple-600" },
        { name: "GraphQL", level: 75, color: "bg-pink-600" },
      ],
    },
    {
      title: t("toolsTechnologies"),
      icon: <GitBranch className="w-6 h-6" />,
      color: "from-purple-500 to-pink-500",
      skills: [
        { name: "Git/GitHub", level: 90, color: "bg-gray-800" },
        { name: "Docker", level: 75, color: "bg-blue-600" },
        { name: "AWS", level: 70, color: "bg-orange-600" },
        { name: "Vercel", level: 85, color: "bg-black" },
        { name: "Firebase", level: 80, color: "bg-yellow-600" },
        { name: "Linux", level: 78, color: "bg-yellow-500" },
      ],
    },
    {
      title: t("mobileDesign"),
      icon: <Smartphone className="w-6 h-6" />,
      color: "from-indigo-500 to-purple-500",
      skills: [
        { name: "React Native", level: 80, color: "bg-blue-500" },
        { name: "Flutter", level: 70, color: "bg-blue-400" },
        { name: "Responsive Design", level: 95, color: "bg-green-500" },
        { name: "PWA", level: 85, color: "bg-purple-500" },
        { name: "UI/UX Design", level: 82, color: "bg-pink-500" },
      ],
    },
  ];

  return (
    <section id="skills" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{t("technicalSkills")}</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t("skillsDescription")}
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {skillCategories.map((category, index) => (
            <Card key={index} className="shadow-lg hover:shadow-2xl transition-all duration-300 hover:scale-105 border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className={`flex items-center gap-3 mb-2 ${flexRow}`}>
                  <div className={`p-2 rounded-lg bg-gradient-to-r ${category.color} text-white`}>
                    {category.icon}
                  </div>
                  <CardTitle className="text-lg text-gray-900">{category.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {category.skills.map((skill, skillIndex) => (
                    <div key={skillIndex} className="group">
                      <div className="flex justify-between items-center mb-2">
                        <div className={`flex items-center gap-2 ${flexRow}`}>
                          <div className={`w-2 h-2 rounded-full ${skill.color}`}></div>
                          <span className="text-sm font-medium text-gray-700 group-hover:text-gray-900 transition-colors">
                            {skill.name}
                          </span>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {skill.level}%
                        </Badge>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                        <div
                          className={`${skill.color} h-2 rounded-full transition-all duration-1000 ease-out transform origin-left hover:scale-y-125`}
                          style={{ width: `${skill.level}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};
