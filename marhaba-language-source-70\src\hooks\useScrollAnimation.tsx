import { useEffect, useRef, useState } from 'react';

interface UseScrollAnimationOptions {
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
}

export const useScrollAnimation = (options: UseScrollAnimationOptions = {}) => {
  const {
    threshold = 0.1,
    rootMargin = '0px 0px -100px 0px',
    triggerOnce = true
  } = options;

  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const elementRef = useRef<any>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          if (triggerOnce) {
            setHasAnimated(true);
          }
        } else if (!triggerOnce && !hasAnimated) {
          setIsVisible(false);
        }
      },
      {
        threshold,
        rootMargin,
      }
    );

    const currentElement = elementRef.current;
    if (currentElement) {
      observer.observe(currentElement);
    }

    return () => {
      if (currentElement) {
        observer.unobserve(currentElement);
      }
    };
  }, [threshold, rootMargin, triggerOnce, hasAnimated]);

  return { elementRef, isVisible };
};

// Hook for multiple elements animation
export const useScrollAnimationGroup = (options: UseScrollAnimationOptions = {}) => {
  const [visibleElements, setVisibleElements] = useState<Set<number>>(new Set());
  const elementsRef = useRef<(any | null)[]>([]);

  const {
    threshold = 0.1,
    rootMargin = '0px 0px -50px 0px',
    triggerOnce = true
  } = options;

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const index = elementsRef.current.findIndex(el => el === entry.target);
          if (index !== -1) {
            setVisibleElements(prev => {
              const newSet = new Set(prev);
              if (entry.isIntersecting) {
                newSet.add(index);
              } else if (!triggerOnce) {
                newSet.delete(index);
              }
              return newSet;
            });
          }
        });
      },
      {
        threshold,
        rootMargin,
      }
    );

    elementsRef.current.forEach(element => {
      if (element) {
        observer.observe(element);
      }
    });

    return () => {
      elementsRef.current.forEach(element => {
        if (element) {
          observer.unobserve(element);
        }
      });
    };
  }, [threshold, rootMargin, triggerOnce]);

  const setElementRef = (index: number) => (element: any | null) => {
    elementsRef.current[index] = element;
  };

  const isElementVisible = (index: number) => visibleElements.has(index);

  return { setElementRef, isElementVisible };
};

// Animation variants
export const animationVariants = {
  fadeInUp: {
    initial: 'opacity-0 translate-y-8',
    animate: 'opacity-100 translate-y-0',
    transition: 'transition-all duration-700 ease-out'
  },
  fadeInDown: {
    initial: 'opacity-0 -translate-y-8',
    animate: 'opacity-100 translate-y-0',
    transition: 'transition-all duration-700 ease-out'
  },
  fadeInLeft: {
    initial: 'opacity-0 -translate-x-8',
    animate: 'opacity-100 translate-x-0',
    transition: 'transition-all duration-700 ease-out'
  },
  fadeInRight: {
    initial: 'opacity-0 translate-x-8',
    animate: 'opacity-100 translate-x-0',
    transition: 'transition-all duration-700 ease-out'
  },
  scaleIn: {
    initial: 'opacity-0 scale-95',
    animate: 'opacity-100 scale-100',
    transition: 'transition-all duration-500 ease-out'
  },
  slideInUp: {
    initial: 'opacity-0 translate-y-16',
    animate: 'opacity-100 translate-y-0',
    transition: 'transition-all duration-800 ease-out'
  },
  rotateIn: {
    initial: 'opacity-0 rotate-12 scale-95',
    animate: 'opacity-100 rotate-0 scale-100',
    transition: 'transition-all duration-600 ease-out'
  },
  bounceIn: {
    initial: 'opacity-0 scale-50',
    animate: 'opacity-100 scale-100',
    transition: 'transition-all duration-600 ease-bounce'
  }
};

// Helper function to get animation classes
export const getAnimationClasses = (
  variant: keyof typeof animationVariants,
  isVisible: boolean,
  delay: number = 0
) => {
  const animation = animationVariants[variant];
  const delayClass = delay > 0 ? `delay-${delay}` : '';
  
  return `${animation.transition} ${delayClass} ${
    isVisible ? animation.animate : animation.initial
  }`;
};
