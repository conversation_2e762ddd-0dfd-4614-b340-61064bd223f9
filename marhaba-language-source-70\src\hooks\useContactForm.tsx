
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export const useContactForm = () => {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const submitForm = async (data: ContactFormData) => {
    setLoading(true);
    
    try {
      // Check if Supabase is configured
      if (!supabase) {
        toast({
          title: "خطأ في الإعداد",
          description: "لم يتم تكوين Supabase بعد. يرجى إعداد متغيرات البيئة.",
          variant: "destructive",
        });
        return { success: false, error: 'Supabase not configured' };
      }

      const { data: result, error } = await supabase.functions.invoke('send-contact-email', {
        body: data
      });

      if (error) {
        throw error;
      }

      toast({
        title: "تم إرسال الرسالة بنجاح!",
        description: "شكراً لتواصلك معي. سأرد عليك في أقرب وقت ممكن.",
      });

      return { success: true };
    } catch (error) {
      console.error('Error sending contact form:', error);
      
      toast({
        title: "خطأ في إرسال الرسالة",
        description: "حدث خطأ أثناء إرسال رسالتك. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });

      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  return {
    submitForm,
    loading
  };
};
