
import { Button } from "@/components/ui/button";
import { useTranslation } from "@/hooks/useTranslation";

export const LanguageToggle = () => {
  const { language, setLanguage } = useTranslation();

  return (
    <div className="flex items-center gap-2">
      <Button
        variant={language === 'ar' ? 'default' : 'outline'}
        size="sm"
        onClick={() => setLanguage('ar')}
        className="text-sm"
      >
        عربي
      </Button>
      <Button
        variant={language === 'en' ? 'default' : 'outline'}
        size="sm"
        onClick={() => setLanguage('en')}
        className="text-sm"
      >
        EN
      </Button>
    </div>
  );
};
