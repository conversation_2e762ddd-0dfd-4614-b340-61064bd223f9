
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ExternalLink, Star, Eye, GitBranch } from "lucide-react";
import { GitHubIcon } from "@/components/icons/SocialIcons";
import { useTranslation } from "@/hooks/useTranslation";
import { useRTL } from "@/hooks/useRTL";
import { useScrollAnimation, useScrollAnimationGroup, getAnimationClasses } from "@/hooks/useScrollAnimation";

export const Projects = () => {
  const { t } = useTranslation();
  const { iconLeft } = useRTL();
  const { elementRef: titleRef, isVisible: titleVisible } = useScrollAnimation();
  const { setElementRef, isElementVisible } = useScrollAnimationGroup();

  const projects = [
    {
      title: t("ecommercePlatform"),
      description: t("ecommerceDescription"),
      image: "/placeholder.svg",
      technologies: ["Next.js", "TypeScript", "Stripe", "PostgreSQL", "Tailwind"],
      liveUrl: "#",
      githubUrl: "#",
      stats: { stars: 124, forks: 32, views: "2.1k" },
      status: "completed",
      gradient: "from-blue-500 to-purple-600"
    },
    {
      title: t("taskManagementApp"),
      description: t("taskDescription"),
      image: "/placeholder.svg",
      technologies: ["React", "Node.js", "Socket.io", "MongoDB", "Express"],
      liveUrl: "#",
      githubUrl: "#",
      stats: { stars: 89, forks: 21, views: "1.5k" },
      status: "completed",
      gradient: "from-green-500 to-teal-600"
    },
    {
      title: t("socialMediaDashboard"),
      description: t("socialDescription"),
      image: "/placeholder.svg",
      technologies: ["Next.js", "Chart.js", "Firebase", "Tailwind", "TypeScript"],
      liveUrl: "#",
      githubUrl: "#",
      stats: { stars: 156, forks: 45, views: "3.2k" },
      status: "completed",
      gradient: "from-purple-500 to-pink-600"
    },
    {
      title: t("aiChatBot"),
      description: t("aiChatBotDescription"),
      image: "/placeholder.svg",
      technologies: ["React", "OpenAI", "Node.js", "WebSocket", "TailwindCSS"],
      liveUrl: "#",
      githubUrl: "#",
      stats: { stars: 203, forks: 67, views: "4.8k" },
      status: "in-progress",
      gradient: "from-orange-500 to-red-600"
    },
    {
      title: t("cryptoTracker"),
      description: t("cryptoTrackerDescription"),
      image: "/placeholder.svg",
      technologies: ["Vue.js", "Chart.js", "CoinGecko API", "Vuex", "SCSS"],
      liveUrl: "#",
      githubUrl: "#",
      stats: { stars: 78, forks: 19, views: "1.2k" },
      status: "completed",
      gradient: "from-yellow-500 to-orange-600"
    },
    {
      title: t("weatherApp"),
      description: t("weatherAppDescription"),
      image: "/placeholder.svg",
      technologies: ["React Native", "OpenWeather API", "Redux", "Expo"],
      liveUrl: "#",
      githubUrl: "#",
      stats: { stars: 45, forks: 12, views: "890" },
      status: "completed",
      gradient: "from-cyan-500 to-blue-600"
    },
  ];

  return (
    <section id="projects" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div
          ref={titleRef}
          className={`text-center mb-16 ${getAnimationClasses('fadeInUp', titleVisible)}`}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{t("featuredProjects")}</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t("projectsDescription")}
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <Card
              key={index}
              ref={setElementRef(index)}
              className={`group shadow-lg hover:shadow-2xl transition-all duration-300 hover:scale-105 overflow-hidden border-0 bg-white/90 backdrop-blur-sm ${getAnimationClasses('slideInUp', isElementVisible(index), index * 150)}`}
            >
              <div className={`aspect-video bg-gradient-to-br ${project.gradient} flex items-center justify-center relative overflow-hidden`}>
                <div className="absolute inset-0 bg-black/20"></div>
                <div className="text-4xl text-white font-bold z-10 group-hover:scale-110 transition-transform duration-300">
                  {project.title.split(' ').map(word => word[0]).join('')}
                </div>
                <div className="absolute top-3 right-3">
                  <Badge
                    variant={project.status === 'completed' ? 'default' : 'secondary'}
                    className={`text-xs ${project.status === 'completed' ? 'bg-green-500' : 'bg-yellow-500'} text-white`}
                  >
                    {project.status === 'completed' ? t('completed') : t('inProgress')}
                  </Badge>
                </div>
              </div>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg text-gray-900 group-hover:text-blue-600 transition-colors">
                    {project.title}
                  </CardTitle>
                </div>
                <div className="flex items-center gap-4 text-xs text-gray-500">
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3" />
                    {project.stats.stars}
                  </div>
                  <div className="flex items-center gap-1">
                    <GitBranch className="h-3 w-3" />
                    {project.stats.forks}
                  </div>
                  <div className="flex items-center gap-1">
                    <Eye className="h-3 w-3" />
                    {project.stats.views}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4 text-sm leading-relaxed line-clamp-3">
                  {project.description}
                </p>
                <div className="flex flex-wrap gap-2 mb-6">
                  {project.technologies.map((tech, techIndex) => (
                    <Badge key={techIndex} variant="outline" className="text-xs hover:bg-gray-100 transition-colors">
                      {tech}
                    </Badge>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Button size="sm" className={`flex-1 bg-gradient-to-r ${project.gradient} hover:opacity-90 transition-opacity`}>
                    <ExternalLink className={`h-4 w-4 ${iconLeft}`} />
                    {t("liveDemo")}
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1 hover:bg-gray-50 transition-colors">
                    <Code className={`h-4 w-4 ${iconLeft}`} />
                    {t("code")}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};
