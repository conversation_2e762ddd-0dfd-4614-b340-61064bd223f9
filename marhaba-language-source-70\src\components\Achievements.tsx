import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useTranslation } from "@/hooks/useTranslation";
import {
  Award,
  Trophy,
  Star,
  FileCheck,
  Target,
  TrendingUp,
  Users,
  Code,
  Zap,
  Globe
} from "lucide-react";

export const Achievements = () => {
  const { t } = useTranslation();

  const achievements = [
    {
      title: t("topDeveloper"),
      description: t("topDeveloperDesc"),
      icon: <Trophy className="w-8 h-8" />,
      color: "from-yellow-500 to-orange-500",
      year: "2024",
      category: "recognition"
    },
    {
      title: t("fullStackCertification"),
      description: t("fullStackCertificationDesc"),
      icon: <FileCheck className="w-8 h-8" />,
      color: "from-blue-500 to-purple-500",
      year: "2023",
      category: "certification"
    },
    {
      title: t("openSourceContributor"),
      description: t("openSourceContributorDesc"),
      icon: <Code className="w-8 h-8" />,
      color: "from-green-500 to-teal-500",
      year: "2024",
      category: "contribution"
    },
    {
      title: t("hackathonWinner"),
      description: t("hackathonWinnerDesc"),
      icon: <Zap className="w-8 h-8" />,
      color: "from-purple-500 to-pink-500",
      year: "2023",
      category: "competition"
    },
    {
      title: t("clientSatisfaction"),
      description: t("clientSatisfactionDesc"),
      icon: <Star className="w-8 h-8" />,
      color: "from-indigo-500 to-blue-500",
      year: "2024",
      category: "performance"
    },
    {
      title: t("teamLeadership"),
      description: t("teamLeadershipDesc"),
      icon: <Users className="w-8 h-8" />,
      color: "from-emerald-500 to-green-500",
      year: "2024",
      category: "leadership"
    }
  ];

  const stats = [
    { label: t("projectsDelivered"), value: "50+", icon: <Target className="w-6 h-6" /> },
    { label: t("happyClients"), value: "30+", icon: <Users className="w-6 h-6" /> },
    { label: t("codeCommits"), value: "2000+", icon: <Code className="w-6 h-6" /> },
    { label: t("countriesServed"), value: "15+", icon: <Globe className="w-6 h-6" /> }
  ];

  return (
    <section id="achievements" className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {t("achievementsTitle")}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t("achievementsDescription")}
          </p>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => (
            <Card key={index} className="text-center shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 bg-white/80 backdrop-blur-sm">
              <CardContent className="pt-6">
                <div className="flex justify-center mb-3">
                  <div className="p-3 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white">
                    {stat.icon}
                  </div>
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Achievements Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {achievements.map((achievement, index) => (
            <Card key={index} className="group shadow-lg hover:shadow-2xl transition-all duration-300 hover:scale-105 border-0 bg-white/90 backdrop-blur-sm overflow-hidden">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between mb-3">
                  <div className={`p-3 rounded-lg bg-gradient-to-r ${achievement.color} text-white group-hover:scale-110 transition-transform duration-300`}>
                    {achievement.icon}
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {achievement.year}
                  </Badge>
                </div>
                <CardTitle className="text-lg text-gray-900 group-hover:text-blue-600 transition-colors">
                  {achievement.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-sm leading-relaxed mb-4">
                  {achievement.description}
                </p>
                <Badge 
                  variant="secondary" 
                  className={`text-xs bg-gradient-to-r ${achievement.color} text-white`}
                >
                  {t(achievement.category)}
                </Badge>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};
