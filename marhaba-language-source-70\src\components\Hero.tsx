
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { ArrowDown, Github, Linkedin, Mail, Sparkles, Download } from "lucide-react";
import { useTranslation } from "@/hooks/useTranslation";
import { useRTL } from "@/hooks/useRTL";

export const Hero = () => {
  const { t } = useTranslation();
  const { iconLeft, isRTL } = useRTL();

  return (
    <section id="home" className="pt-20 min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Animated background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="absolute inset-0 bg-grid-pattern opacity-[0.02]"></div>
      </div>
      
      {/* Enhanced floating elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-br from-blue-400/30 to-purple-400/30 rounded-full blur-xl animate-bounce-slow"></div>
      <div className="absolute top-40 right-20 w-32 h-32 bg-gradient-to-br from-purple-400/30 to-pink-400/30 rounded-full blur-2xl animate-float"></div>
      <div className="absolute bottom-40 left-20 w-24 h-24 bg-gradient-to-br from-green-400/30 to-blue-400/30 rounded-full blur-xl animate-pulse-slow"></div>
      <div className="absolute top-60 left-1/2 w-16 h-16 bg-gradient-to-br from-yellow-400/20 to-orange-400/20 rounded-full blur-lg animate-ping"></div>
      <div className="absolute bottom-20 right-10 w-28 h-28 bg-gradient-to-br from-indigo-400/25 to-cyan-400/25 rounded-full blur-xl animate-bounce-slow delay-1000"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <div className="animate-fade-in">
          <div className="mb-8 relative">
            {/* Decorative rings around avatar */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-52 h-52 md:w-60 md:h-60 lg:w-68 lg:h-68 rounded-full border-2 border-blue-200/30 animate-pulse"></div>
            </div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-56 h-56 md:w-64 md:h-64 lg:w-72 lg:h-72 rounded-full border border-purple-200/20 animate-ping"></div>
            </div>

            <Avatar className="w-48 h-48 md:w-56 md:h-56 lg:w-64 lg:h-64 mx-auto mb-6 shadow-2xl ring-4 ring-white/50 group hover:scale-110 transition-all duration-500 relative z-10 avatar-glow">
              <AvatarImage
                src="/lovable-uploads/6d3fd0a4-5be1-42cf-ada4-ae04ab1679d3.png"
                alt="Personal photo"
                className="object-cover"
              />
              <AvatarFallback className="bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 text-white text-6xl md:text-7xl lg:text-8xl font-bold">
                FS
              </AvatarFallback>
            </Avatar>
            <Sparkles className="absolute top-2 right-2 md:top-4 md:right-4 w-8 h-8 md:w-10 md:h-10 text-yellow-400 animate-pulse z-20" />
          </div>
          
          <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight">
            <span className="inline-block bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent animate-gradient-x">
              {t("fullStackDeveloper")}
            </span>
          </h1>
          
          <p className="text-xl md:text-2xl text-gray-600 mb-10 max-w-4xl mx-auto leading-relaxed font-medium">
            {t("heroDescription")}
          </p>
          
          <div className={`flex flex-col sm:flex-row gap-6 justify-center items-center mb-12 ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
            <Button
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-semibold rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group"
            >
              <a href="#projects" className="flex items-center gap-2">
                {t("viewMyWork")}
                <Sparkles className="w-5 h-5 group-hover:rotate-12 transition-transform duration-300" />
              </a>
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-2 border-gray-300 hover:border-purple-500 text-gray-700 hover:text-purple-600 px-8 py-4 text-lg font-semibold rounded-full hover:shadow-lg transform hover:scale-105 transition-all duration-300 bg-white/80 backdrop-blur-sm"
            >
              <a href="#contact">{t("getInTouch")}</a>
            </Button>
            <Button
              variant="secondary"
              size="lg"
              className="border-2 border-emerald-300 hover:border-emerald-500 text-emerald-700 hover:text-emerald-600 px-8 py-4 text-lg font-semibold rounded-full hover:shadow-lg transform hover:scale-105 transition-all duration-300 bg-emerald-50/80 backdrop-blur-sm group"
            >
              <a href="/resume.pdf" download className="flex items-center gap-2">
                <Download className={`w-5 h-5 group-hover:translate-y-1 transition-transform duration-300 ${iconLeft}`} />
                {t("downloadCV")}
              </a>
            </Button>
          </div>
          
          <div className={`flex justify-center mb-12 ${isRTL ? 'space-x-reverse space-x-8' : 'space-x-8'}`}>
            <a
              href="https://github.com"
              className="group p-3 rounded-full bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl text-gray-600 hover:text-blue-600 transition-all duration-300 hover:scale-110"
              aria-label="GitHub Profile"
            >
              <Github className="h-6 w-6 group-hover:rotate-12 transition-transform duration-300" />
            </a>
            <a
              href="https://linkedin.com"
              className="group p-3 rounded-full bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl text-gray-600 hover:text-blue-600 transition-all duration-300 hover:scale-110"
              aria-label="LinkedIn Profile"
            >
              <Linkedin className="h-6 w-6 group-hover:rotate-12 transition-transform duration-300" />
            </a>
            <a
              href="mailto:<EMAIL>"
              className="group p-3 rounded-full bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl text-gray-600 hover:text-blue-600 transition-all duration-300 hover:scale-110"
              aria-label="Send Email"
            >
              <Mail className="h-6 w-6 group-hover:rotate-12 transition-transform duration-300" />
            </a>
          </div>
          
          <div className="animate-bounce">
            <a href="#about" className="group">
              <div className="w-12 h-12 mx-auto bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                <ArrowDown className="h-6 w-6 text-gray-400 group-hover:text-blue-500 transition-colors duration-300" />
              </div>
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};
