
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useTranslation } from "@/hooks/useTranslation";
import { useRTL } from "@/hooks/useRTL";
import { Code, Zap, Users, Award } from "lucide-react";

export const About = () => {
  const { t } = useTranslation();
  const { flexRow } = useRTL();

  return (
    <section id="about" className="py-20 bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-10 left-10 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-2xl"></div>
      <div className="absolute bottom-10 right-10 w-40 h-40 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-2xl"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{t("aboutMe")}</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t("aboutSubtitle")}
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <Card className="p-8 shadow-xl hover:shadow-2xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm">
              <CardContent className="p-0">
                <div className={`flex items-center gap-3 mb-6 ${flexRow}`}>
                  <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 text-white">
                    <Code className="w-6 h-6" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">{t("myJourney")}</h3>
                </div>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {t("aboutDescription1")}
                </p>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {t("aboutDescription2")}
                </p>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors">
                    React
                  </Badge>
                  <Badge variant="secondary" className="bg-green-100 text-green-800 hover:bg-green-200 transition-colors">
                    Next.js
                  </Badge>
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200 transition-colors">
                    Node.js
                  </Badge>
                  <Badge variant="secondary" className="bg-purple-100 text-purple-800 hover:bg-purple-200 transition-colors">
                    TypeScript
                  </Badge>
                  <Badge variant="secondary" className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200 transition-colors">
                    PostgreSQL
                  </Badge>
                  <Badge variant="secondary" className="bg-pink-100 text-pink-800 hover:bg-pink-200 transition-colors">
                    MongoDB
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="space-y-6">
            <Card className="group bg-gradient-to-r from-blue-500 to-purple-600 p-8 text-white shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border-0">
              <div className={`flex items-center gap-4 ${flexRow}`}>
                <div className="p-3 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors">
                  <Zap className="w-8 h-8" />
                </div>
                <div>
                  <h4 className="text-2xl font-bold mb-2">3+ {t("yearsExperience")}</h4>
                  <p className="text-blue-100">{t("professionalExperience")}</p>
                </div>
              </div>
            </Card>

            <Card className="group bg-gradient-to-r from-green-500 to-teal-500 p-8 text-white shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border-0">
              <div className={`flex items-center gap-4 ${flexRow}`}>
                <div className="p-3 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors">
                  <Award className="w-8 h-8" />
                </div>
                <div>
                  <h4 className="text-2xl font-bold mb-2">50+ {t("projectsCompleted")}</h4>
                  <p className="text-green-100">{t("successfullyCompleted")}</p>
                </div>
              </div>
            </Card>

            <Card className="group bg-gradient-to-r from-purple-500 to-pink-500 p-8 text-white shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border-0">
              <div className={`flex items-center gap-4 ${flexRow}`}>
                <div className="p-3 bg-white/20 rounded-lg group-hover:bg-white/30 transition-colors">
                  <Users className="w-8 h-8" />
                </div>
                <div>
                  <h4 className="text-2xl font-bold mb-2">{t("fullStack")}</h4>
                  <p className="text-purple-100">{t("frontendBackendExpert")}</p>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};
