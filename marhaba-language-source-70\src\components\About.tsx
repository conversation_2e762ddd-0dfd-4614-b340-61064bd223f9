
import { Card, CardContent } from "@/components/ui/card";
import { useTranslation } from "@/hooks/useTranslation";

export const About = () => {
  const { t } = useTranslation();

  return (
    <section id="about" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{t("aboutMe")}</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t("aboutSubtitle")}
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <Card className="p-8 shadow-lg">
              <CardContent className="p-0">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">{t("myJourney")}</h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {t("aboutDescription1")}
                </p>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {t("aboutDescription2")}
                </p>
                <div className="flex flex-wrap gap-2">
                  <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">React</span>
                  <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Next.js</span>
                  <span className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">Node.js</span>
                  <span className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">TypeScript</span>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="space-y-6">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-8 rounded-lg text-white">
              <h4 className="text-xl font-bold mb-2">3+ {t("yearsExperience")}</h4>
              <p>{t("professionalExperience")}</p>
            </div>
            <div className="bg-gradient-to-r from-green-500 to-blue-500 p-8 rounded-lg text-white">
              <h4 className="text-xl font-bold mb-2">20+ {t("projectsCompleted")}</h4>
              <p>{t("successfullyCompleted")}</p>
            </div>
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-8 rounded-lg text-white">
              <h4 className="text-xl font-bold mb-2">{t("fullStack")}</h4>
              <p>{t("frontendBackendExpert")}</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
