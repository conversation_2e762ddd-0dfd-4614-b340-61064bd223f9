import { useState, useEffect } from 'react';

interface UseCountUpOptions {
  start?: number;
  end: number;
  duration?: number;
  decimals?: number;
  suffix?: string;
  prefix?: string;
}

export const useCountUp = (options: UseCountUpOptions, trigger: boolean = true) => {
  const {
    start = 0,
    end,
    duration = 2000,
    decimals = 0,
    suffix = '',
    prefix = ''
  } = options;

  const [count, setCount] = useState(start);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (!trigger || isAnimating) return;

    setIsAnimating(true);
    const startTime = Date.now();
    const startValue = start;
    const endValue = end;
    const totalChange = endValue - startValue;

    const animateCount = () => {
      const now = Date.now();
      const elapsed = now - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Easing function (ease-out)
      const easeOut = 1 - Math.pow(1 - progress, 3);
      const currentValue = startValue + (totalChange * easeOut);

      setCount(currentValue);

      if (progress < 1) {
        requestAnimationFrame(animateCount);
      } else {
        setCount(endValue);
        setIsAnimating(false);
      }
    };

    requestAnimationFrame(animateCount);
  }, [trigger, start, end, duration, isAnimating]);

  const formatValue = (value: number) => {
    const formattedNumber = value.toFixed(decimals);
    return `${prefix}${formattedNumber}${suffix}`;
  };

  return {
    value: formatValue(count),
    rawValue: count,
    isAnimating
  };
};

// Hook for multiple counters
export const useMultipleCountUp = (counters: UseCountUpOptions[], trigger: boolean = true) => {
  const results = counters.map(options => useCountUp(options, trigger));
  
  return {
    values: results.map(result => result.value),
    rawValues: results.map(result => result.rawValue),
    isAnimating: results.some(result => result.isAnimating)
  };
};
