
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { useTranslation } from "@/hooks/useTranslation";

export const Experience = () => {
  const { t } = useTranslation();

  const experiences = [
    {
      title: t("seniorFullStackDeveloper"),
      company: t("techSolutionsInc"),
      period: `2022 - ${t("present")}`,
      description: [
        t("exp1_1"),
        t("exp1_2"),
        t("exp1_3"),
        t("exp1_4")
      ],
    },
    {
      title: t("fullStackDeveloperRole"),
      company: t("digitalAgencyPro"),
      period: "2021 - 2022",
      description: [
        t("exp2_1"),
        t("exp2_2"),
        t("exp2_3"),
        t("exp2_4")
      ],
    },
    {
      title: t("frontendDeveloper"),
      company: t("startupXYZ"),
      period: "2020 - 2021",
      description: [
        t("exp3_1"),
        t("exp3_2"),
        t("exp3_3"),
        t("exp3_4")
      ],
    },
  ];

  return (
    <section id="experience" className="py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl translate-x-1/2 translate-y-1/2"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-16">
          <div className="inline-block">
            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-4 animate-fade-in">
              {t("workExperience")}
            </h2>
            <div className="h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transform scale-x-0 animate-[scale-x-100_1s_ease-out_0.5s_forwards]"></div>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mt-6 animate-fade-in">
            {t("experienceDescription")}
          </p>
        </div>
        
        <div className="space-y-8">
          {experiences.map((exp, index) => (
            <Card 
              key={index} 
              className="group shadow-lg hover:shadow-2xl transition-all duration-500 border-0 bg-white/80 backdrop-blur-sm hover:bg-white/90 hover:scale-[1.02] animate-fade-in relative overflow-hidden"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              {/* Card background gradient */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-50/50 via-transparent to-purple-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              
              <CardHeader className="relative">
                <div className="flex flex-col md:flex-row md:justify-between md:items-center">
                  <div>
                    <CardTitle className="text-xl lg:text-2xl text-gray-900 mb-2 group-hover:text-blue-700 transition-colors duration-300">
                      {exp.title}
                    </CardTitle>
                    <p className="text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                      {exp.company}
                    </p>
                  </div>
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 mt-2 md:mt-0">
                    {exp.period}
                  </span>
                </div>
              </CardHeader>
              <CardContent className="relative">
                <ul className="space-y-3">
                  {exp.description.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start group/item">
                      <div className="flex-shrink-0 w-2 h-2 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 mt-2.5 mr-3 group-hover/item:scale-125 transition-transform duration-200"></div>
                      <span className="text-gray-600 group-hover/item:text-gray-700 transition-colors duration-200 leading-relaxed">
                        {item}
                      </span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};
