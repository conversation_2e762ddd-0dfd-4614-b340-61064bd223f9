
import { createContext, useContext, useState, ReactNode } from 'react';

type Language = 'ar' | 'en';

interface TranslationContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const translations = {
  ar: {
    // Navigation
    home: "الرئيسية",
    about: "نبذة عني",
    skills: "المهارات",
    experience: "الخبرة",
    projects: "المشاريع",
    contact: "التواصل",
    portfolio: "معرض الأعمال",

    // Hero Section
    fullStackDeveloper: "مطور ويب متكامل",
    heroDescription: "أقوم ببناء تجارب رقمية استثنائية باستخدام تقنيات الويب الحديثة. شغوف بإنشاء تطبيقات قابلة للتوسع باستخدام Next.js وReact وNode.js.",
    viewMyWork: "عرض أعمالي",
    getInTouch: "تواصل معي",

    // About Section
    aboutMe: "نبذة عني",
    aboutSubtitle: "مطور ويب متكامل شغوف بخبرة في تقنيات الويب الحديثة",
    myJourney: "رحلتي",
    aboutDescription1: "أنا مطور ويب متكامل شغوف بخبرة تزيد عن 3 سنوات في بناء تطبيقات الويب. أتخصص في نظام JavaScript البيئي، مع خبرة عميقة في React وNext.js وNode.js وتقنيات قواعد البيانات الحديثة.",
    aboutDescription2: "أحب إنشاء كود نظيف وفعال وبناء تطبيقات سهلة الاستخدام تحل مشاكل العالم الحقيقي. عندما لا أكون أبرمج، يمكنك أن تجدني أستكشف تقنيات جديدة أو أساهم في مشاريع مفتوحة المصدر.",
    yearsExperience: "سنوات خبرة",
    professionalExperience: "خبرة مهنية",
    projectsCompleted: "مشروع مكتمل",
    successfullyCompleted: "مكتمل بنجاح",
    fullStack: "متكامل",
    frontendBackendExpert: "خبير الواجهة الأمامية والخلفية",

    // Skills Section
    technicalSkills: "المهارات التقنية",
    skillsDescription: "هذه هي التقنيات والأدوات التي أعمل بها لتحويل الأفكار إلى واقع",
    frontendDevelopment: "تطوير الواجهة الأمامية",
    backendDevelopment: "تطوير الواجهة الخلفية",
    toolsTechnologies: "الأدوات والتقنيات",

    // Experience Section
    workExperience: "الخبرة المهنية",
    experienceDescription: "رحلتي المهنية والتأثير الذي أحدثته",
    seniorFullStackDeveloper: "مطور ويب متكامل أول",
    techSolutionsInc: "شركة التقنيات المتكاملة",
    present: "الحاضر",
    fullStackDeveloperRole: "مطور ويب متكامل",
    digitalAgencyPro: "وكالة التسويق الرقمي برو",
    frontendDeveloper: "مطور واجهة أمامية",
    startupXYZ: "شركة ناشئة XYZ",
    
    // Experience descriptions
    exp1_1: "قدت تطوير عدة تطبيقات ويب باستخدام Next.js وNode.js",
    exp1_2: "نفذت بنية الخدمات المصغرة مما قلل أوقات التحميل بنسبة 40%",
    exp1_3: "وجهت المطورين المبتدئين وأجريت مراجعات للكود",
    exp1_4: "تعاونت مع فريق التصميم لإنشاء مكونات واجهة متجاوبة",
    exp2_1: "طورت وصنت تطبيقات React لعملاء متنوعين",
    exp2_2: "بنيت واجهات برمجة تطبيقات RESTful باستخدام Express.js وMongoDB",
    exp2_3: "نفذت أنظمة المصادقة والتفويض",
    exp2_4: "حسنت أداء التطبيقات واستعلامات قواعد البيانات",
    exp3_1: "أنشأت واجهات ويب متجاوبة باستخدام React وأطر CSS",
    exp3_2: "تعاونت مع مصممي UX/UI لتنفيذ تصاميم دقيقة",
    exp3_3: "دمجت واجهات برمجة التطبيقات والخدمات الخارجية",
    exp3_4: "شاركت في عمليات التطوير الرشيقة",

    // Projects Section
    featuredProjects: "المشاريع المميزة",
    projectsDescription: "هنا بعض مشاريعي الحديثة التي تعرض مهاراتي وخبرتي",
    ecommercePlatform: "منصة التجارة الإلكترونية",
    ecommerceDescription: "حل تجارة إلكترونية متكامل مع Next.js، تكامل Stripe، ولوحة تحكم إدارية. يتضمن إدارة المنتجات وتتبع الطلبات ومعالجة المدفوعات.",
    taskManagementApp: "تطبيق إدارة المهام",
    taskDescription: "تطبيق إدارة مهام تعاوني مع تحديثات فورية ووظائف السحب والإفلات وميزات التعاون الجماعي.",
    socialMediaDashboard: "لوحة تحكم وسائل التواصل",
    socialDescription: "لوحة تحكم تحليلية لإدارة وسائل التواصل الاجتماعي مع تصور البيانات وميزات الجدولة وتكامل متعدد المنصات.",
    liveDemo: "عرض مباشر",
    code: "الكود",

    // Contact Section
    getInTouchTitle: "تواصل معي",
    contactDescription: "مستعد لبدء مشروعك القادم؟ لنعمل معاً لإنشاء شيء مذهل",
    letsConnect: "لنتواصل",
    contactText: "أنا دائماً مهتم بسماع الفرص الجديدة والمشاريع المثيرة. سواء كنت شركة تبحث عن التوظيف، أو زميل مطور يود التواصل، أحب أن أسمع منك.",
    email: "البريد الإلكتروني",
    phone: "الهاتف",
    location: "الموقع",
    remote: "عن بُعد / مفتوح للانتقال",
    sendMessage: "إرسال رسالة",
    name: "الاسم",
    yourName: "اسمك",
    yourEmail: "بريدك الإلكتروني",
    subject: "الموضوع",
    projectDiscussion: "مناقشة مشروع",
    message: "الرسالة",
    messagePlaceholder: "أخبرني عن مشروعك...",
    sendMessageBtn: "إرسال الرسالة"
  },
  en: {
    // Navigation
    home: "Home",
    about: "About",
    skills: "Skills",
    experience: "Experience",
    projects: "Projects",
    contact: "Contact",
    portfolio: "Portfolio",

    // Hero Section
    fullStackDeveloper: "Full Stack Developer",
    heroDescription: "I build exceptional digital experiences with modern web technologies. Passionate about creating scalable applications using Next.js, React, and Node.js.",
    viewMyWork: "View My Work",
    getInTouch: "Get In Touch",

    // About Section
    aboutMe: "About Me",
    aboutSubtitle: "Passionate Full Stack Developer with expertise in modern web technologies",
    myJourney: "My Journey",
    aboutDescription1: "I'm a passionate Full Stack Developer with over 3 years of experience building web applications. I specialize in JavaScript ecosystem, with deep expertise in React, Next.js, Node.js, and modern database technologies.",
    aboutDescription2: "I love creating clean, efficient code and building user-friendly applications that solve real-world problems. When I'm not coding, you can find me exploring new technologies or contributing to open-source projects.",
    yearsExperience: "Years",
    professionalExperience: "Professional Experience",
    projectsCompleted: "Projects",
    successfullyCompleted: "Successfully Completed",
    fullStack: "Full Stack",
    frontendBackendExpert: "Frontend & Backend Expert",

    // Skills Section
    technicalSkills: "Technical Skills",
    skillsDescription: "Here are the technologies and tools I work with to bring ideas to life",
    frontendDevelopment: "Frontend Development",
    backendDevelopment: "Backend Development",
    toolsTechnologies: "Tools & Technologies",

    // Experience Section
    workExperience: "Work Experience",
    experienceDescription: "My professional journey and the impact I've made",
    seniorFullStackDeveloper: "Senior Full Stack Developer",
    techSolutionsInc: "Tech Solutions Inc.",
    present: "Present",
    fullStackDeveloperRole: "Full Stack Developer",
    digitalAgencyPro: "Digital Agency Pro",
    frontendDeveloper: "Frontend Developer",
    startupXYZ: "StartupXYZ",
    
    // Experience descriptions
    exp1_1: "Led development of multiple web applications using Next.js and Node.js",
    exp1_2: "Implemented microservices architecture reducing load times by 40%",
    exp1_3: "Mentored junior developers and conducted code reviews",
    exp1_4: "Collaborated with design team to create responsive UI components",
    exp2_1: "Developed and maintained React applications for various clients",
    exp2_2: "Built RESTful APIs using Express.js and MongoDB",
    exp2_3: "Implemented authentication and authorization systems",
    exp2_4: "Optimized application performance and database queries",
    exp3_1: "Created responsive web interfaces using React and CSS frameworks",
    exp3_2: "Collaborated with UX/UI designers to implement pixel-perfect designs",
    exp3_3: "Integrated third-party APIs and services",
    exp3_4: "Participated in agile development processes",

    // Projects Section
    featuredProjects: "Featured Projects",
    projectsDescription: "Here are some of my recent projects that showcase my skills and expertise",
    ecommercePlatform: "E-Commerce Platform",
    ecommerceDescription: "Full-stack e-commerce solution with Next.js, Stripe integration, and admin dashboard. Features include product management, order tracking, and payment processing.",
    taskManagementApp: "Task Management App",
    taskDescription: "Collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.",
    socialMediaDashboard: "Social Media Dashboard",
    socialDescription: "Analytics dashboard for social media management with data visualization, scheduling features, and multi-platform integration.",
    liveDemo: "Live Demo",
    code: "Code",

    // Contact Section
    getInTouchTitle: "Get In Touch",
    contactDescription: "Ready to start your next project? Let's work together to create something amazing",
    letsConnect: "Let's Connect",
    contactText: "I'm always interested in hearing about new opportunities and exciting projects. Whether you're a company looking to hire, or you're a fellow developer who'd like to connect, I'd love to hear from you.",
    email: "Email",
    phone: "Phone",
    location: "Location",
    remote: "Remote / Open to Relocation",
    sendMessage: "Send a Message",
    name: "Name",
    yourName: "Your Name",
    yourEmail: "<EMAIL>",
    subject: "Subject",
    projectDiscussion: "Project Discussion",
    message: "Message",
    messagePlaceholder: "Tell me about your project...",
    sendMessageBtn: "Send Message"
  }
};

const TranslationContext = createContext<TranslationContextType | null>(null);

export const TranslationProvider = ({ children }: { children: ReactNode }) => {
  const [language, setLanguage] = useState<Language>('ar');

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations['ar']] || key;
  };

  return (
    <TranslationContext.Provider value={{ language, setLanguage, t }}>
      <div dir={language === 'ar' ? 'rtl' : 'ltr'} className={language === 'ar' ? 'font-cairo' : 'font-inter'}>
        {children}
      </div>
    </TranslationContext.Provider>
  );
};

export const useTranslation = () => {
  const context = useContext(TranslationContext);
  if (!context) {
    throw new Error('useTranslation must be used within TranslationProvider');
  }
  return context;
};
