
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Send, User, Mail, MessageSquare, FileText } from 'lucide-react';
import { useContactForm } from '@/hooks/useContactForm';
import { useTranslation } from '@/hooks/useTranslation';
import { useRTL } from '@/hooks/useRTL';

export const ContactForm = () => {
  const { t } = useTranslation();
  const { submitForm, loading } = useContactForm();
  const { flexRow, iconLeft } = useRTL();
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const result = await submitForm(formData);
    
    if (result.success) {
      setFormData({
        name: '',
        email: '',
        subject: '',
        message: ''
      });
    }
  };

  return (
    <Card className="bg-white/80 backdrop-blur-sm shadow-xl border-0 hover:shadow-2xl transition-all duration-300">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          {t("getInTouch")}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name" className={`text-gray-700 font-medium flex items-center gap-2 ${flexRow}`}>
                <User className="w-4 h-4" />
                {t("name")}
              </Label>
              <Input
                id="name"
                name="name"
                type="text"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 transition-colors"
                placeholder={t("enterYourName")}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email" className={`text-gray-700 font-medium flex items-center gap-2 ${flexRow}`}>
                <Mail className="w-4 h-4" />
                {t("email")}
              </Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 transition-colors"
                placeholder={t("enterYourEmail")}
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="subject" className={`text-gray-700 font-medium flex items-center gap-2 ${flexRow}`}>
              <FileText className="w-4 h-4" />
              {t("subject")}
            </Label>
            <Input
              id="subject"
              name="subject"
              type="text"
              value={formData.subject}
              onChange={handleInputChange}
              required
              className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 transition-colors"
              placeholder={t("enterSubject")}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="message" className={`text-gray-700 font-medium flex items-center gap-2 ${flexRow}`}>
              <MessageSquare className="w-4 h-4" />
              {t("message")}
            </Label>
            <Textarea
              id="message"
              name="message"
              value={formData.message}
              onChange={handleInputChange}
              required
              rows={5}
              className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 transition-colors resize-none"
              placeholder={t("enterYourMessage")}
            />
          </div>
          
          <Button
            type="submit"
            disabled={loading}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                {t("sending")}
              </div>
            ) : (
              <div className={`flex items-center gap-2 ${flexRow}`}>
                <Send className="w-5 h-5" />
                {t("sendMessage")}
              </div>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};
